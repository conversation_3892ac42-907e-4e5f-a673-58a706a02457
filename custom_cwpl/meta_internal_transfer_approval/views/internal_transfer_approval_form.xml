<?xml version="1.0" ?>
<odoo>
    <record id="view_internal_return_approval_form" model="ir.ui.view">
        <field name="name">view.internal.return.approval.form</field>
        <field name="model">internal.return.approval</field>
        <field name="arch" type="xml">
            <form string="Internal and Return Approval" create="false" delete="false">
                <header>
                    <button name="send_to_store" invisible="state != 'draft'" string="Sent to Store" class="oe_highlight"
                        type="object"/>

                    <button name="store_approve" invisible="state != 'store'" string="Approve" class="oe_highlight"
                        type="object" groups="meta_internal_transfer_approval.internal_store_approval"/>
                    <button name="store_cancel" invisible="state != 'store'" string="Cancel" class="oe_highlight"
                        type="object" groups="meta_internal_transfer_approval.internal_store_approval"/>

                    <button name="scm_approve" invisible="state != 'scm'" string="Approve" class="oe_highlight"
                        type="object" groups="meta_internal_transfer_approval.internal_scm_approval"/>
                    <button name="scm_cancel" invisible="state != 'scm'" string="Cancel" class="oe_highlight"
                        type="object" groups="meta_internal_transfer_approval.internal_scm_approval"/>

                    <button name="crm_approve" invisible="state != 'crm'" string="Approve" class="oe_highlight"
                        type="object" groups="meta_internal_transfer_approval.return_crm_approval"/>
                    <button name="crm_cancel" invisible="state != 'crm'" string="Cancel" class="oe_highlight"
                        type="object" groups="meta_internal_transfer_approval.return_crm_approval"/>

                    <field name="state" widget="statusbar" statusbar_visible="store,scm,crm,confirm"/>
                </header>
                <sheet>
                    <div class="oe_edit_only">
                        <label for="name" class="oe_inline" />
                    </div>
                    <h1>
                        <field name="name" class="oe_inline" readonly="1"/>
                    </h1>
                    <group>
                        <group>
                            <field name="partner_id" readonly="1"/>
                            <field name="picking_type" readonly="1"/>
                            <field name="location_id" readonly="1"/>
                            <field name="location_dest_id" readonly="1"/>
                            <field name="picking_id" readonly="1"/>
                            <field name="transfer_type" invisible="0" readonly="1"/>
                        </group>
                        <group>
                            <field name="date" readonly="1"/>
                            <field name="landed_cost_id" readonly="1"/>
                            <field name="origin" readonly="1"/>
                            <field name="dg_plant_id" readonly="1"/>
                            <field name="cwpl_contact" readonly="1"/>
                            <field name="received_by" readonly="1"/>
                            <field name="customer_name" readonly="1"/>
                            <field name="customer_reference" readonly="1"/>
                            <field name="site_id" readonly="1"/>
                            <field name="delivery_location" readonly="1"/>
                        </group>
                    </group>

                    <notebook>
                        <page string="Detailed Operations" name="detailed_operations">
                            <field name="detailed_operation_ids" mode="list" add-label="Product" readonly="1">
                                <list string="Stock Moves Line" editable="bottom">
                                    <field name="internal_approval_id" invisible="1"/>
                                    <field name="move_line_id" invisible="1"/>
                                    <field name="mv_line_id" invisible="1"/>
                                    <field name="product_id" />
                                    <field name="location_id"/>
                                    <field name="lot_id" />
                                    <field name="product_uom_qty"/>
                                    <field name="qty_done"/>
                                    <field name="product_uom_id"/>
                                </list>
                            </field>
                        </page>

                        <page string="Operations" name="operations">
                            <field name="operation_ids" mode="list" add-label="Product" readonly="1">
                                <list string="Stock Moves" editable="bottom">
                                    <field name="internal_approval_id" invisible="1"/>
                                    <field name="move_id" invisible="1"/>
                                    <field name="mv_id" invisible="1"/>
                                    <field name="product_id" />
                                    <field name="product_uom_qty"/>
                                    <field name="qty_done" />
                                    <field name="product_uom_id"/>
                                </list>
                            </field>
                        </page>

                    </notebook>
                </sheet>

                <div class="oe_chatter">
                    <field name="message_follower_ids" widget="mail_followers" />
                    <field name="activity_ids" widget="mail_activity" />
                    <field name="message_ids" widget="mail_thread" />
                </div>
            </form>
        </field>
    </record>

    <record id="internal_return_approval_view_tree" model="ir.ui.view">
        <field name="name">internal.return.approval.view.tree</field>
        <field name="model">internal.return.approval</field>
        <field name="arch" type="xml">
            <list string="Internal and Return Approval" create="false" delete="false">
                <field name="name"/>
                <field name="picking_id"/>
                <field name="picking_type"/>
                <field name="date"/>
                <field name="state" widget="badge"/>
            </list>
        </field>
    </record>

    <record model="ir.actions.act_window" id="internal_transfer_approval_form_action">
        <field name="name">Internal-Return Approval</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">internal.return.approval</field>
        <field name="view_mode">list,form</field>
        <field name="context"></field>
        <field name="help" type="html">
            <p class="oe_view_nocontent_create">
                Click to start a new Transfer approval.
            </p>
        </field>
    </record>

    <menuitem
        id="menu_transfer_approval_act"
        name="Internal Transfer Approval"
        sequence="11"
        parent="stock.menu_stock_warehouse_mgmt"
        action="internal_transfer_approval_form_action"/>
</odoo>